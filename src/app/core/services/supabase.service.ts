import { Injectable } from '@angular/core';
import {
  createClient,
  Session,
  SupabaseClient,
  User,
} from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { ConfigService } from './';

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  metadata?: any;
}

export interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root',
})
export class SupabaseService {
  private readonly supabase!: SupabaseClient;
  private authStateSubject = new BehaviorSubject<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
  });

  public authState$ = this.authStateSubject.asObservable();

  constructor(private configService: ConfigService) {
    const supabaseConfig = this.configService.supabase;

    if (!supabaseConfig.url || !supabaseConfig.anonKey) {
      console.warn('⚠️ Supabase configuration missing');
      this.updateAuthState({
        loading: false,
        error: null,
      });
      return;
    }

    this.supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true, // Allow session persistence for remember me
        detectSessionInUrl: true, // Enable URL session detection for invites
        flowType: 'pkce',
        lock: async (
          name: string,
          acquireTimeout: number,
          fn: () => Promise<any>,
        ) => {
          // Custom lock implementation that doesn't use Navigator.locks
          return await fn();
        },
      },
    });

    // Always start with no session - force login
    this.initializeAuth();
  }

  // Getters
  get currentUser(): AuthUser | null {
    return this.authStateSubject.value.user;
  }

  get currentSession(): Session | null {
    return this.authStateSubject.value.session;
  }

  get isAuthenticated(): boolean {
    return !!this.authStateSubject.value.user;
  }

  get isLoading(): boolean {
    return this.authStateSubject.value.loading;
  }

  // Database access (for when you need it)
  get client(): SupabaseClient {
    return this.supabase;
  }

  // Authentication methods
  async signInWithEmail(
    email: string,
    password: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        let friendlyError = error.message;

        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          friendlyError = 'Invalid email or password';
        } else if (error.message.includes('Email not confirmed')) {
          friendlyError = 'Please check your email and confirm your account';
        } else if (error.message.includes('Too many requests')) {
          friendlyError = 'Too many login attempts. Please try again later';
        }

        this.updateAuthState({ loading: false, error: friendlyError });
        return { success: false, error: friendlyError };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signUpWithEmail(
    email: string,
    password: string,
    metadata?: any,
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signInWithProvider(
    provider: 'google' | 'github' | 'discord',
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signOut(): Promise<void> {
    try {
      this.updateAuthState({ loading: true });
      // Clear remember me preference
      localStorage.removeItem('chainmatic-remember-me');
      await this.supabase.auth.signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  async resetPassword(
    email: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async verifyInviteToken(): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      // Get session from URL (for invite flows)
      const { data, error } = await this.supabase.auth.getSession();

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      if (data.session?.user) {
        // Session is valid, user is authenticated
        this.updateAuthState({
          user: this.mapUser(data.session.user),
          session: data.session,
          loading: false,
          error: null,
        });
        return { success: true };
      } else {
        // No valid session found
        this.updateAuthState({ loading: false, error: null });
        return { success: false, error: 'No valid session found' };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  isInviteUrl(): boolean {
    // Check if current URL contains invite tokens
    const hash = window.location.hash;
    return hash.includes('access_token=') && hash.includes('type=invite');
  }

  // Utility methods
  hasRole(role: string): boolean {
    return this.currentUser?.role === role;
  }

  hasAnyRole(roles: string[]): boolean {
    return roles.includes(this.currentUser?.role || '');
  }

  private async initializeAuth(): Promise<void> {
    try {
      // Check if user had "remember me" enabled
      const rememberMe = localStorage.getItem('chainmatic-remember-me') === 'true';

      if (rememberMe) {
        // If remember me is enabled, check for existing session
        const { data: { session }, error } = await this.supabase.auth.getSession();

        if (!error && session?.user) {
          this.updateAuthState({
            user: this.mapUser(session.user),
            session,
            loading: false,
            error: null,
          });
        } else {
          // Session expired or invalid, start fresh
          this.updateAuthState({
            user: null,
            session: null,
            loading: false,
            error: null,
          });
        }
      } else {
        // No remember me - always start fresh
        this.updateAuthState({
          user: null,
          session: null,
          loading: false,
          error: null,
        });
      }

      // Listen for auth changes
      this.supabase.auth.onAuthStateChange((event, session) => {
        console.log('Auth state changed:', event);

        try {
          if (event === 'SIGNED_IN' && session?.user) {
            this.updateAuthState({
              user: this.mapUser(session.user),
              session,
              loading: false,
              error: null,
            });
          } else if (event === 'SIGNED_OUT') {
            // Clear remember me on sign out
            localStorage.removeItem('chainmatic-remember-me');
            this.updateAuthState({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          } else if (event === 'INITIAL_SESSION' && !rememberMe) {
            // If no remember me, ignore initial session
            this.updateAuthState({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          }
        } catch (error) {
          console.error('Error handling auth state change:', error);
        }
      });
    } catch (error) {
      console.error('Error initializing auth:', error);
      // Don't show initialization errors to user
      this.updateAuthState({
        loading: false,
        error: null,
      });
    }
  }

  private mapUser(user: User): AuthUser {
    return {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.['name'] || user.user_metadata?.['full_name'],
      avatar: user.user_metadata?.['avatar_url'],
      role: user.user_metadata?.['role'] || 'user',
      metadata: user.user_metadata,
    };
  }

  private updateAuthState(updates: Partial<AuthState>): void {
    const currentState = this.authStateSubject.value;
    this.authStateSubject.next({ ...currentState, ...updates });
  }
}
