import { Injectable } from '@angular/core';
import {
  createClient,
  Session,
  SupabaseClient,
  User,
} from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { ConfigService } from './';

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  metadata?: any;
}

export interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root',
})
export class SupabaseService {
  private readonly supabase!: SupabaseClient;
  private authStateSubject = new BehaviorSubject<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
  });

  public authState$ = this.authStateSubject.asObservable();

  constructor(private configService: ConfigService) {
    const supabaseConfig = this.configService.supabase;

    if (!supabaseConfig.url || !supabaseConfig.anonKey) {
      console.warn('⚠️ Supabase configuration missing');
      this.updateAuthState({
        loading: false,
        error: null,
      });
      return;
    }

    this.supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true, // Allow session persistence for remember me
        detectSessionInUrl: true, // Enable URL session detection for invites
        flowType: 'pkce',
        lock: async (
          name: string,
          acquireTimeout: number,
          fn: () => Promise<any>,
        ) => {
          // Custom lock implementation that doesn't use Navigator.locks
          return await fn();
        },
      },
    });

    // Always start with no session - force login
    this.initializeAuth();
  }

  // Getters
  get currentUser(): AuthUser | null {
    return this.authStateSubject.value.user;
  }

  get currentSession(): Session | null {
    return this.authStateSubject.value.session;
  }

  get isAuthenticated(): boolean {
    return !!this.authStateSubject.value.user;
  }

  get isLoading(): boolean {
    return this.authStateSubject.value.loading;
  }

  // Database access (for when you need it)
  get client(): SupabaseClient {
    return this.supabase;
  }

  // Authentication methods
  async signInWithEmail(
    email: string,
    password: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        let friendlyError = error.message;

        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          friendlyError = 'Invalid email or password';
        } else if (error.message.includes('Email not confirmed')) {
          friendlyError = 'Please check your email and confirm your account';
        } else if (error.message.includes('Too many requests')) {
          friendlyError = 'Too many login attempts. Please try again later';
        }

        this.updateAuthState({ loading: false, error: friendlyError });
        return { success: false, error: friendlyError };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signUpWithEmail(
    email: string,
    password: string,
    metadata?: any,
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signInWithProvider(
    provider: 'google' | 'github' | 'discord',
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  async signOut(): Promise<void> {
    try {
      this.updateAuthState({ loading: true });
      // Clear remember me preference
      localStorage.removeItem('chainmatic-remember-me');
      await this.supabase.auth.signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  async resetPassword(
    email: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async updatePassword(
    newPassword: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      const { error } = await this.supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      this.updateAuthState({ loading: false, error: null });
      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  private currentMfaFactor: any = null; // Store the current MFA factor

  async setupMfa(): Promise<{
    success: boolean;
    error?: string;
    qrCodeUrl?: string;
    secret?: string;
  }> {
    try {
      const { data, error } = await this.supabase.auth.mfa.enroll({
        factorType: 'totp',
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data) {
        // Store the factor for later verification
        this.currentMfaFactor = data;
        console.log('🔐 MFA factor created:', data.id);

        return {
          success: true,
          qrCodeUrl: data.totp.qr_code,
          secret: data.totp.secret,
        };
      }

      return { success: false, error: 'No MFA data received' };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async verifyMfa(code: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get the current MFA enrollment
      const { data: factors } = await this.supabase.auth.mfa.listFactors();

      if (!factors || !factors.totp || factors.totp.length === 0) {
        return { success: false, error: 'No TOTP factors found' };
      }

      const factor = factors.totp[0]; // Use the first TOTP factor

      // Create a challenge first
      const { data: challenge, error: challengeError } = await this.supabase.auth.mfa.challenge({
        factorId: factor.id,
      });

      if (challengeError || !challenge) {
        return { success: false, error: challengeError?.message || 'Failed to create challenge' };
      }

      // Verify the code
      const { error } = await this.supabase.auth.mfa.verify({
        factorId: factor.id,
        challengeId: challenge.id,
        code,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async verifyInviteToken(): Promise<{
    success: boolean;
    error?: string;
    needsPasswordSetup?: boolean;
  }> {
    try {
      this.updateAuthState({ loading: true, error: null });

      // Extract tokens from URL (either hash or returnUrl parameter)
      const tokens = this.extractInviteTokens();

      if (tokens) {
        // Set the session using the extracted tokens
        const { data, error } = await this.supabase.auth.setSession({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
        });

        if (error) {
          this.updateAuthState({ loading: false, error: error.message });
          return { success: false, error: error.message };
        }

        if (data.session?.user) {
          // Session is valid, user is authenticated
          this.updateAuthState({
            user: this.mapUser(data.session.user),
            session: data.session,
            loading: false,
            error: null,
          });

          // Check if user needs to set up password
          const needsPasswordSetup = this.checkIfPasswordSetupNeeded(
            data.session.user,
          );

          return {
            success: true,
            needsPasswordSetup,
          };
        }
      }

      // Fallback: try to get existing session
      const { data, error } = await this.supabase.auth.getSession();

      if (error) {
        this.updateAuthState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      if (data.session?.user) {
        // Session is valid, user is authenticated
        this.updateAuthState({
          user: this.mapUser(data.session.user),
          session: data.session,
          loading: false,
          error: null,
        });

        // Check if user needs to set up password
        const needsPasswordSetup = this.checkIfPasswordSetupNeeded(
          data.session.user,
        );

        return {
          success: true,
          needsPasswordSetup,
        };
      } else {
        // No valid session found
        this.updateAuthState({ loading: false, error: null });
        return { success: false, error: 'No valid session found' };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.updateAuthState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  isInviteUrl(): boolean {
    // Check if current URL contains invite tokens in hash
    const hash = window.location.hash;
    if (hash.includes('access_token=') && hash.includes('type=invite')) {
      return true;
    }

    // Check if invite tokens are in returnUrl query parameter (URL encoded)
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('returnUrl');
    if (returnUrl) {
      const decodedReturnUrl = decodeURIComponent(returnUrl);
      return (
        decodedReturnUrl.includes('access_token=') &&
        decodedReturnUrl.includes('type=invite')
      );
    }

    // Check if the current URL itself is an invite URL (direct from Supabase)
    const currentUrl = window.location.href;
    return (
      currentUrl.includes('access_token=') && currentUrl.includes('type=invite')
    );
  }

  // Utility methods
  hasRole(role: string): boolean {
    return this.currentUser?.role === role;
  }

  hasAnyRole(roles: string[]): boolean {
    return roles.includes(this.currentUser?.role || '');
  }

  private checkIfPasswordSetupNeeded(user: User): boolean {
    // For now, assume all invite users need password setup
    // This can be refined based on your specific Supabase configuration
    const isFromInvite = this.isInviteUrl();

    return isFromInvite;
  }

  private extractInviteTokens(): {
    access_token: string;
    refresh_token: string;
  } | null {
    console.log('🔍 Extracting invite tokens from URL...');
    console.log('Current URL:', window.location.href);
    console.log('Hash:', window.location.hash);
    console.log('Search:', window.location.search);

    // First try to extract from hash
    const hash = window.location.hash.substring(1); // Remove #
    if (hash.includes('access_token=')) {
      console.log('📍 Found tokens in hash');
      const hashParams = new URLSearchParams(hash);
      const accessToken = hashParams.get('access_token');
      const refreshToken = hashParams.get('refresh_token');

      if (accessToken && refreshToken) {
        console.log('✅ Successfully extracted tokens from hash');
        return { access_token: accessToken, refresh_token: refreshToken };
      }
    }

    // Then try to extract from returnUrl parameter
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('returnUrl');
    if (returnUrl) {
      console.log('📍 Checking returnUrl parameter');
      const decodedReturnUrl = decodeURIComponent(returnUrl);
      console.log('Decoded returnUrl:', decodedReturnUrl);

      // Extract the fragment part after #
      const fragmentIndex = decodedReturnUrl.indexOf('#');
      if (fragmentIndex !== -1) {
        const fragment = decodedReturnUrl.substring(fragmentIndex + 1);
        console.log('Fragment from returnUrl:', fragment);
        const fragmentParams = new URLSearchParams(fragment);

        const accessToken = fragmentParams.get('access_token');
        const refreshToken = fragmentParams.get('refresh_token');

        if (accessToken && refreshToken) {
          console.log('✅ Successfully extracted tokens from returnUrl');
          return { access_token: accessToken, refresh_token: refreshToken };
        }
      }
    }

    // Finally, try to extract from current URL query parameters (direct invite)
    console.log('📍 Checking current URL query parameters');
    const currentUrlParams = new URLSearchParams(window.location.search);
    const accessToken = currentUrlParams.get('access_token');
    const refreshToken = currentUrlParams.get('refresh_token');

    if (accessToken && refreshToken) {
      console.log('✅ Successfully extracted tokens from query params');
      return { access_token: accessToken, refresh_token: refreshToken };
    }

    console.log('❌ No invite tokens found');
    return null;
  }

  private async initializeAuth(): Promise<void> {
    try {
      // Check if user had "remember me" enabled
      const rememberMe =
        localStorage.getItem('chainmatic-remember-me') === 'true';

      if (rememberMe) {
        // If remember me is enabled, check for existing session
        const {
          data: { session },
          error,
        } = await this.supabase.auth.getSession();

        if (!error && session?.user) {
          this.updateAuthState({
            user: this.mapUser(session.user),
            session,
            loading: false,
            error: null,
          });
        } else {
          // Session expired or invalid, start fresh
          this.updateAuthState({
            user: null,
            session: null,
            loading: false,
            error: null,
          });
        }
      } else {
        // No remember me - always start fresh
        this.updateAuthState({
          user: null,
          session: null,
          loading: false,
          error: null,
        });
      }

      // Listen for auth changes
      this.supabase.auth.onAuthStateChange((event, session) => {
        console.log('Auth state changed:', event);

        try {
          if (event === 'SIGNED_IN' && session?.user) {
            this.updateAuthState({
              user: this.mapUser(session.user),
              session,
              loading: false,
              error: null,
            });
          } else if (event === 'SIGNED_OUT') {
            // Clear remember me on sign out
            localStorage.removeItem('chainmatic-remember-me');
            this.updateAuthState({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          } else if (event === 'INITIAL_SESSION' && !rememberMe) {
            // If no remember me, ignore initial session
            this.updateAuthState({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          }
        } catch (error) {
          console.error('Error handling auth state change:', error);
        }
      });
    } catch (error) {
      console.error('Error initializing auth:', error);
      // Don't show initialization errors to user
      this.updateAuthState({
        loading: false,
        error: null,
      });
    }
  }

  private mapUser(user: User): AuthUser {
    return {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.['name'] || user.user_metadata?.['full_name'],
      avatar: user.user_metadata?.['avatar_url'],
      role: user.user_metadata?.['role'] || 'user',
      metadata: user.user_metadata,
    };
  }

  private updateAuthState(updates: Partial<AuthState>): void {
    const currentState = this.authStateSubject.value;
    this.authStateSubject.next({ ...currentState, ...updates });
  }
}
