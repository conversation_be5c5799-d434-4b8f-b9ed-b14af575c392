import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// PrimeNG
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { SupabaseService } from '../../core';

@Component({
  selector: 'chm-password-setup',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
  ],
  templateUrl: './password-setup.component.html',
  styleUrls: ['./password-setup.component.css'],
})
export class PasswordSetupComponent implements OnInit {
  passwordForm: FormGroup;
  loading = false;
  error: string | null = null;
  userEmail = '';

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.passwordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
    }, { validators: this.passwordMatchValidator });
  }

  // Getters for template
  get logoUrl(): string {
    return 'images/chainmatic-portal.png';
  }

  get password() { return this.passwordForm.get('password'); }
  get confirmPassword() { return this.passwordForm.get('confirmPassword'); }

  ngOnInit(): void {
    // Get user email from current session if available
    const currentUser = this.supabaseService.currentUser;
    if (currentUser?.email) {
      this.userEmail = currentUser.email;
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  async onSubmit(): Promise<void> {
    if (this.passwordForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const { password } = this.passwordForm.value;

      // Update user password
      const result = await this.supabaseService.updatePassword(password);

      if (result.success) {
        console.log('✅ Password setup successful');

        // Get return URL from query params or default to dashboard
        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';

        // Redirect to the intended page
        this.router.navigate([returnUrl]);
      } else {
        this.error = result.error || 'Failed to set password';
        this.loading = false;
      }
    } catch (error) {
      console.error('❌ Password setup error:', error);
      this.error = 'An unexpected error occurred';
      this.loading = false;
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.passwordForm.controls).forEach(key => {
      const control = this.passwordForm.get(key);
      control?.markAsTouched();
    });
  }


}
