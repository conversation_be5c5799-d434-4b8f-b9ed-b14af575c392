import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { SupabaseService } from '../../core';

@Component({
  selector: 'chm-auth-callback',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './callback.component.html',
  styleUrls: ['./callback.component.css'],
})
export class AuthCallbackComponent implements OnInit {
  loading = true;
  error: string | null = null;
  success = false;

  constructor(
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  // Getters for template
  get logoUrl(): string {
    return 'images/chainmatic-portal.png';
  }

  async ngOnInit(): Promise<void> {
    try {
      // Check if this is an invite URL
      if (this.supabaseService.isInviteUrl()) {
        console.log('🔗 Processing invite URL...');
        await this.handleInviteFlow();
      } else {
        // Handle regular auth callback (OAuth, etc.)
        await this.handleAuthCallback();
      }
    } catch (error) {
      console.error('❌ Auth callback error:', error);
      this.error = 'Authentication failed. Please try again.';
      this.loading = false;
    }
  }

  private async handleInviteFlow(): Promise<void> {
    try {
      const result = await this.supabaseService.verifyInviteToken();

      if (result.success) {
        console.log('✅ Invite verification successful');
        this.success = true;
        this.loading = false;

        // Get return URL from query params or default to dashboard
        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';
        
        // Redirect after a short delay to show success message
        setTimeout(() => {
          this.router.navigate([returnUrl]);
        }, 2000);
      } else {
        console.error('❌ Invite verification failed:', result.error);
        this.error = result.error || 'Invalid or expired invite link';
        this.loading = false;

        // Redirect to login after delay
        setTimeout(() => {
          this.router.navigate(['/auth/login']);
        }, 3000);
      }
    } catch (error) {
      console.error('❌ Invite flow error:', error);
      this.error = 'Failed to process invite. Please try again.';
      this.loading = false;
    }
  }

  private async handleAuthCallback(): Promise<void> {
    try {
      // For OAuth or other auth callbacks
      const result = await this.supabaseService.verifyInviteToken();

      if (result.success) {
        console.log('✅ Auth callback successful');
        this.success = true;
        this.loading = false;

        // Get return URL from query params or default to dashboard
        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';
        
        // Redirect after a short delay
        setTimeout(() => {
          this.router.navigate([returnUrl]);
        }, 1500);
      } else {
        console.error('❌ Auth callback failed:', result.error);
        this.error = result.error || 'Authentication failed';
        this.loading = false;

        // Redirect to login after delay
        setTimeout(() => {
          this.router.navigate(['/auth/login']);
        }, 3000);
      }
    } catch (error) {
      console.error('❌ Auth callback error:', error);
      this.error = 'Authentication failed. Please try again.';
      this.loading = false;
    }
  }

  onRetryClick(): void {
    this.router.navigate(['/auth/login']);
  }
}
